name: 'Create Patch PR'

on:
  workflow_dispatch:
    inputs:
      commit:
        description: 'The commit SHA to cherry-pick for the patch.'
        required: true
        type: 'string'
      channel:
        description: 'The release channel to patch.'
        required: true
        type: 'choice'
        options:
          - 'stable'
          - 'preview'
      dry_run:
        description: 'Whether to run in dry-run mode.'
        required: false
        type: 'boolean'
        default: false

jobs:
  create-patch:
    runs-on: 'ubuntu-latest'
    permissions:
      contents: 'write'
      pull-requests: 'write'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        with:
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: 'npm ci'

      - name: 'Configure Git User'
        run: |-
          git config user.name "gemini-cli-robot"
          git config user.email "<EMAIL>"

      - name: 'Create Patch for Stable'
        if: "github.event.inputs.channel == 'stable'"
        env:
          GH_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: 'node scripts/create-patch-pr.js --commit=${{ github.event.inputs.commit }} --channel=stable --dry-run=${{ github.event.inputs.dry_run }}'

      - name: 'Create Patch for Preview'
        env:
          GH_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: 'node scripts/create-patch-pr.js --commit=${{ github.event.inputs.commit }} --channel=${{ github.event.inputs.channel }} --dry-run=${{ github.event.inputs.dry_run }}'

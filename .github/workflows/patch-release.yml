name: 'Patch Release'

on:
  workflow_dispatch:
    inputs:
      type:
        description: 'The type of release to patch from.'
        required: true
        type: 'choice'
        options:
          - 'stable'
          - 'preview'
      ref:
        description: 'The branch or ref (full git sha) to release from.'
        required: true
        type: 'string'
        default: 'main'
      dry_run:
        description: 'Run a dry-run of the release process; no branches, npm packages or GitHub releases will be created.'
        required: true
        type: 'boolean'
        default: true
      force_skip_tests:
        description: 'Select to skip the "Run Tests" step in testing. Prod releases should run tests'
        required: false
        type: 'boolean'
        default: false

jobs:
  release:
    runs-on: 'ubuntu-latest'
    environment:
      name: 'production-release'
      url: '${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ steps.version.outputs.RELEASE_TAG }}'
    if: |-
      ${{ github.repository == 'google-gemini/gemini-cli' }}
    permissions:
      contents: 'write'
      packages: 'write'
      id-token: 'write'
      issues: 'write' # For creating issues on failure
    outputs:
      RELEASE_TAG: '${{ steps.version.outputs.RELEASE_TAG }}'

    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        with:
          ref: '${{ github.event.inputs.ref || github.sha }}'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: |-
          npm ci

      - name: 'Get the version'
        id: 'version'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: |-
          VERSION_JSON="$(node scripts/get-release-version.js --type=patch --patch-from=${{ github.event.inputs.type }})"
          echo "${VERSION_JSON}"
          echo "RELEASE_TAG=$(echo "${VERSION_JSON}" | jq -r .releaseTag)" >> "${GITHUB_OUTPUT}"
          echo "RELEASE_VERSION=$(echo "${VERSION_JSON}" | jq -r .releaseVersion)" >> "${GITHUB_OUTPUT}"
          echo "NPM_TAG=$(echo "${VERSION_JSON}" | jq -r .npmTag)" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_TAG=$(echo "${VERSION_JSON}" | jq -r .previousReleaseTag)" >> "${GITHUB_OUTPUT}"

      - name: 'Print Calculated Version'
        run: |-
          echo "Calculated version: ${{ steps.version.outputs.RELEASE_VERSION }}"

      - name: 'Run Tests'
        uses: './.github/actions/run-tests'
        with:
          force_skip_tests: '${{ github.event.inputs.force_skip_tests }}'
          gemini_api_key: '${{ secrets.GEMINI_API_KEY }}'

      - name: 'Publish Release'
        uses: './.github/actions/publish-release'
        with:
          release-version: '${{ steps.version.outputs.RELEASE_VERSION }}'
          release-tag: '${{ steps.version.outputs.RELEASE_TAG }}'
          npm-tag: '${{ steps.version.outputs.NPM_TAG }}'
          wombat-token-core: '${{ secrets.WOMBAT_TOKEN_CORE }}'
          wombat-token-cli: '${{ secrets.WOMBAT_TOKEN_CLI }}'
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          dry-run: '${{ github.event.inputs.dry_run }}'
          previous-tag: '${{ steps.version.outputs.PREVIOUS_TAG }}'

name: 'Release'

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'The version to release (e.g., v0.1.11).'
        required: true
        type: 'string'
      ref:
        description: 'The branch or ref (full git sha) to release from.'
        required: true
        type: 'string'
        default: 'main'
      dry_run:
        description: 'Run a dry-run of the release process; no branches, npm packages or GitHub releases will be created.'
        required: true
        type: 'boolean'
        default: true
      force_skip_tests:
        description: 'Select to skip the "Run Tests" step in testing. Prod releases should run tests'
        required: false
        type: 'boolean'
        default: false

jobs:
  release:
    runs-on: 'ubuntu-latest'
    environment:
      name: 'production-release'
      url: '${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ steps.version.outputs.RELEASE_TAG }}'
    if: |-
      ${{ github.repository == 'google-gemini/gemini-cli' }}
    permissions:
      contents: 'write'
      packages: 'write'
      id-token: 'write'
      issues: 'write' # For creating issues on failure
    outputs:
      RELEASE_TAG: '${{ steps.version.outputs.RELEASE_TAG }}'

    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        with:
          ref: '${{ github.event.inputs.ref || github.sha }}'
          fetch-depth: 0

      - name: 'Set booleans for simplified logic'
        env:
          DRY_RUN_INPUT: '${{ github.event.inputs.dry_run }}'
        id: 'vars'
        run: |-
          is_dry_run="false"
          if [[ "${DRY_RUN_INPUT}" == "true" ]]; then
            is_dry_run="true"
          fi
          echo "is_dry_run=${is_dry_run}" >> "${GITHUB_OUTPUT}"

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: |-
          npm ci

      - name: 'Get the version'
        id: 'version'
        run: |-
          RELEASE_TAG="${{ inputs.version }}"
          # The version for npm should not have the 'v' prefix.
          RELEASE_VERSION="${RELEASE_TAG#v}"
          NPM_TAG="latest"
          if [[ "${RELEASE_TAG}" == *"preview"* ]]; then
            NPM_TAG="preview"
          fi
          PREVIOUS_TAG=$(git describe --tags --abbrev=0)
          echo "RELEASE_TAG=${RELEASE_TAG}" >> "${GITHUB_OUTPUT}"
          echo "RELEASE_VERSION=${RELEASE_VERSION}" >> "${GITHUB_OUTPUT}"
          echo "NPM_TAG=${NPM_TAG}" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_TAG=${PREVIOUS_TAG}" >> "${GITHUB_OUTPUT}"

      - name: 'Run Tests'
        if: |-
          ${{ github.event.inputs.force_skip_tests != 'true' }}
        env:
          GEMINI_API_KEY: '${{ secrets.GEMINI_API_KEY }}'
        run: |-
          npm run preflight
          npm run test:integration:sandbox:none
          npm run test:integration:sandbox:docker

      - name: 'Configure Git User'
        run: |-
          git config user.name "gemini-cli-robot"
          git config user.email "<EMAIL>"

      - name: 'Create and switch to a release branch'
        id: 'release_branch'
        env:
          RELEASE_TAG: '${{ steps.version.outputs.RELEASE_TAG }}'
        run: |-
          BRANCH_NAME="release/${RELEASE_TAG}"
          git switch -c "${BRANCH_NAME}"
          echo "BRANCH_NAME=${BRANCH_NAME}" >> "${GITHUB_OUTPUT}"

      - name: 'Update package versions'
        env:
          RELEASE_VERSION: '${{ steps.version.outputs.RELEASE_VERSION }}'
        run: |-
          npm run release:version "${RELEASE_VERSION}"

      - name: 'Commit and Conditionally Push package versions'
        env:
          BRANCH_NAME: '${{ steps.release_branch.outputs.BRANCH_NAME }}'
          IS_DRY_RUN: '${{ steps.vars.outputs.is_dry_run }}'
          RELEASE_TAG: '${{ steps.version.outputs.RELEASE_TAG }}'
        run: |-
          git add package.json npm-shrinkwrap.json packages/*/package.json
          git commit -m "chore(release): ${RELEASE_TAG}"
          if [[ "${IS_DRY_RUN}" == "false" ]]; then
            echo "Pushing release branch to remote..."
            git push --set-upstream origin "${BRANCH_NAME}" --follow-tags
          else
            echo "Dry run enabled. Skipping push."
          fi

      - name: 'Build and Prepare Packages'
        run: |-
          npm run build:packages
          npm run prepare:package

      - name: 'Configure npm for publishing'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          registry-url: 'https://wombat-dressing-room.appspot.com'
          scope: '@google'

      - name: 'Publish @google/gemini-cli-core'
        env:
          IS_DRY_RUN: '${{ steps.vars.outputs.is_dry_run }}'
          NODE_AUTH_TOKEN: '${{ secrets.WOMBAT_TOKEN_CORE }}'
          NPM_TAG: '${{ steps.version.outputs.NPM_TAG }}'
        run: |-
          npm publish \
            --dry-run="${IS_DRY_RUN}" \
            --workspace="@google/gemini-cli-core" \
            --tag="${NPM_TAG}"

      - name: 'Install latest core package'
        if: |-
          ${{ steps.vars.outputs.is_dry_run == 'false' }}
        env:
          RELEASE_VERSION: '${{ steps.version.outputs.RELEASE_VERSION }}'
        run: |-
          npm install "@google/gemini-cli-core@${RELEASE_VERSION}" \
          --workspace="@google/gemini-cli" \
          --save-exact

      - name: 'Publish @google/gemini-cli'
        env:
          IS_DRY_RUN: '${{ steps.vars.outputs.is_dry_run }}'
          NODE_AUTH_TOKEN: '${{ secrets.WOMBAT_TOKEN_CLI }}'
          NPM_TAG: '${{ steps.version.outputs.NPM_TAG }}'
        run: |-
          npm publish \
            --dry-run="${IS_DRY_RUN}" \
            --workspace="@google/gemini-cli" \
            --tag="${NPM_TAG}"

      - name: 'Create GitHub Release and Tag'
        if: |-
          ${{ steps.vars.outputs.is_dry_run == 'false' }}
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          RELEASE_BRANCH: '${{ steps.release_branch.outputs.BRANCH_NAME }}'
          RELEASE_TAG: '${{ steps.version.outputs.RELEASE_TAG }}'
          PREVIOUS_TAG: '${{ steps.version.outputs.PREVIOUS_TAG }}'
        run: |-
          gh release create "${RELEASE_TAG}" \
            bundle/gemini.js \
            --target "$RELEASE_BRANCH" \
            --title "Release ${RELEASE_TAG}" \
            --notes-start-tag "$PREVIOUS_TAG" \
            --generate-notes

      - name: 'Create Issue on Failure'
        if: |-
          ${{ failure() }}
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          RELEASE_TAG: '${{ steps.version.outputs.RELEASE_TAG }} || "N/A"'
          DETAILS_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
        run: |-
          gh issue create \
            --title "Release Failed for ${RELEASE_TAG} on $(date +'%Y-%m-%d')" \
            --body "The release workflow failed. See the full run for details: ${DETAILS_URL}" \
            --label "kind/bug,release-failure,priority/p0"
